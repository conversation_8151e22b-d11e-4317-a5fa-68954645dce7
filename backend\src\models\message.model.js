import mongoose from 'mongoose'

const messageSchema = mongoose.Schema({
    receiver: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
        required: true,
    },
    sender: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
        required: true,
    },
    text: {
        type: String,
        required: true,
    },
}, {
    timestamps: true,
})

const messageModel = mongoose.model("message", messageSchema);

export default messageModel;
