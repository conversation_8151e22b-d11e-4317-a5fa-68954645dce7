{"name": "backend", "version": "2.0.1", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node --inspect --watch-path=./ ./server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@google/genai": "^1.10.0", "bcryptjs": "^3.0.2", "cookie": "^1.0.2", "cookie-parser": "^1.4.7", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "multer": "^2.0.2", "socket.io": "^4.8.1", "uuid": "^11.1.0"}}