import { Server } from 'socket.io'
import config from '../config/config.js'
import cookie from 'cookie'
import jwt from 'jsonwebtoken'
import { createMessage } from '../dao/message.dao.js'

const users = {}

function setupSocket(server) {

    const io = new Server(server, {})

    io.use((socket, next) => {
        const cookies = socket.request.headers.cookie
        const { token } = cookie.parse(cookies || "")

        if (!token) {
            return next(new Error('Authentication Error'))
        }
        try {
            const decoded = jwt.verify(token, config.jwtSecret)
            socket.user = decoded
            next()
        } catch (error) {
            return next(new Error('Authentication Error'))
        }
    })

    io.on("connection", (socket) => {

        users[socket.user.id] = socket.id

        console.log(users)

        socket.on("disconnect", () => {
            console.log('A user disconnect')
        })

        socket.on("message", async (msg) => {
            const { receiver, message } = msg
            socket.to(users[receiver]).emit("message", message)

            await createMessage({
                receiver: receiver,
                sender: socket.user.id,
                text: message
            })
        })

    })

}

export default setupSocket;